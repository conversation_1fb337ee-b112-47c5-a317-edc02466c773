{"file:///Users/<USER>/Documents/note/python/todo/eigent-main/backend/app/component/code.py": {"language": "Python", "code": 11, "comment": 0, "blank": 6}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/backend/app/component/encrypt.py": {"language": "Python", "code": 6, "comment": 0, "blank": 6}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/backend/app/model/chat.py": {"language": "Python", "code": 83, "comment": 1, "blank": 27}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/backend/app/component/model_validation.py": {"language": "Python", "code": 9, "comment": 29, "blank": 4}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/backend/app/component/environment.py": {"language": "Python", "code": 53, "comment": 18, "blank": 28}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/backend/app/component/debug.py": {"language": "Python", "code": 10, "comment": 3, "blank": 3}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/backend/app/component/babel.py": {"language": "Python", "code": 8, "comment": 0, "blank": 3}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/backend/app/middleware/__init__.py": {"language": "Python", "code": 4, "comment": 0, "blank": 3}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/backend/app/exception/handler.py": {"language": "Python", "code": 37, "comment": 0, "blank": 11}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/backend/app/component/pydantic/i18n.py": {"language": "Python", "code": 34, "comment": 15, "blank": 10}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/backend/app/exception/exception.py": {"language": "Python", "code": 14, "comment": 0, "blank": 7}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/backend/app/controller/__init__.py": {"language": "Python", "code": 0, "comment": 0, "blank": 1}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/backend/app/component/pydantic/translations/zh_CN.json": {"language": "JSON", "code": 98, "comment": 0, "blank": 1}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/backend/app/service/task.py": {"language": "Python", "code": 261, "comment": 9, "blank": 91}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/backend/app/component/command.py": {"language": "Python", "code": 5, "comment": 0, "blank": 5}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/backend/app/component/pydantic/translations/en_US.json": {"language": "JSON", "code": 98, "comment": 0, "blank": 1}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/backend/app/command/__init__.py": {"language": "Python", "code": 3, "comment": 0, "blank": 3}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/backend/app/service/chat_service.py": {"language": "Python", "code": 290, "comment": 172, "blank": 24}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/backend/app/controller/model_controller.py": {"language": "Python", "code": 35, "comment": 0, "blank": 9}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/backend/app/controller/chat_controller.py": {"language": "Python", "code": 64, "comment": 1, "blank": 19}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/backend/app/controller/tool_controller.py": {"language": "Python", "code": 13, "comment": 0, "blank": 6}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/backend/app/controller/task_controller.py": {"language": "Python", "code": 40, "comment": 0, "blank": 13}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/backend/app/__init__.py": {"language": "Python", "code": 4, "comment": 0, "blank": 3}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/backend/app/utils/single_agent_worker.py": {"language": "Python", "code": 43, "comment": 148, "blank": 7}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/backend/app/utils/workforce.py": {"language": "Python", "code": 212, "comment": 27, "blank": 33}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/backend/app/utils/toolkit/human_toolkit.py": {"language": "Python", "code": 61, "comment": 70, "blank": 8}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/backend/app/utils/agent.py": {"language": "Python", "code": 865, "comment": 358, "blank": 105}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/backend/app/utils/listen/toolkit_listen.py": {"language": "Python", "code": 152, "comment": 7, "blank": 19}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/backend/app/utils/toolkit/markitdown_toolkit.py": {"language": "Python", "code": 13, "comment": 0, "blank": 6}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/backend/app/utils/toolkit/hybrid_browser_python_toolkit.py": {"language": "Python", "code": 176, "comment": 193, "blank": 36}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/backend/app/utils/toolkit/video_download_toolkit.py": {"language": "Python", "code": 38, "comment": 0, "blank": 8}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/backend/app/utils/toolkit/github_toolkit.py": {"language": "Python", "code": 95, "comment": 0, "blank": 13}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/backend/app/utils/toolkit/abstract_toolkit.py": {"language": "Python", "code": 11, "comment": 1, "blank": 5}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/backend/app/utils/toolkit/twitter_toolkit.py": {"language": "Python", "code": 65, "comment": 0, "blank": 11}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/backend/app/utils/toolkit/video_analysis_toolkit.py": {"language": "Python", "code": 40, "comment": 0, "blank": 6}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/backend/app/utils/toolkit/image_analysis_toolkit.py": {"language": "Python", "code": 34, "comment": 0, "blank": 7}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/backend/app/utils/toolkit/audio_analysis_toolkit.py": {"language": "Python", "code": 30, "comment": 0, "blank": 7}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/backend/app/utils/toolkit/google_calendar_toolkit.py": {"language": "Python", "code": 50, "comment": 0, "blank": 10}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/backend/app/utils/toolkit/web_deploy_toolkit.py": {"language": "Python", "code": 45, "comment": 0, "blank": 9}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/backend/app/utils/toolkit/notion_toolkit.py": {"language": "Python", "code": 43, "comment": 0, "blank": 8}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/backend/app/utils/toolkit/hybrid_browser_toolkit.py": {"language": "Python", "code": 215, "comment": 9, "blank": 37}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/backend/app/utils/toolkit/slack_toolkit.py": {"language": "Python", "code": 66, "comment": 0, "blank": 12}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/backend/app/utils/toolkit/mcp_search_toolkit.py": {"language": "Python", "code": 44, "comment": 10, "blank": 6}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/backend/app/utils/toolkit/linkedin_toolkit.py": {"language": "Python", "code": 35, "comment": 0, "blank": 8}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/backend/app/utils/toolkit/thinking_toolkit.py": {"language": "Python", "code": 29, "comment": 0, "blank": 12}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/backend/app/utils/toolkit/notion_mcp_toolkit.py": {"language": "Python", "code": 42, "comment": 0, "blank": 5}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/backend/app/utils/toolkit/pyautogui_toolkit.py": {"language": "Python", "code": 76, "comment": 0, "blank": 14}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/backend/app/utils/toolkit/whatsapp_toolkit.py": {"language": "Python", "code": 39, "comment": 0, "blank": 8}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/backend/app/utils/toolkit/reddit_toolkit.py": {"language": "Python", "code": 62, "comment": 0, "blank": 8}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/backend/app/utils/toolkit/file_write_toolkit.py": {"language": "Python", "code": 52, "comment": 0, "blank": 5}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/backend/app/utils/toolkit/screenshot_toolkit.py": {"language": "Python", "code": 21, "comment": 0, "blank": 7}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/backend/app/utils/toolkit/excel_toolkit.py": {"language": "Python", "code": 21, "comment": 0, "blank": 6}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/backend/app/utils/toolkit/search_toolkit.py": {"language": "Python", "code": 120, "comment": 157, "blank": 25}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/backend/app/utils/toolkit/craw4ai_toolkit.py": {"language": "Python", "code": 14, "comment": 7, "blank": 9}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/backend/app/utils/toolkit/note_taking_toolkit.py": {"language": "Python", "code": 33, "comment": 0, "blank": 9}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/backend/app/utils/toolkit/pptx_toolkit.py": {"language": "Python", "code": 38, "comment": 0, "blank": 7}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/backend/app/utils/toolkit/code_execution_toolkit.py": {"language": "Python", "code": 33, "comment": 0, "blank": 7}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/backend/app/utils/toolkit/google_drive_mcp_toolkit.py": {"language": "Python", "code": 41, "comment": 0, "blank": 5}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/backend/app/utils/toolkit/google_gmail_mcp_toolkit.py": {"language": "Python", "code": 46, "comment": 0, "blank": 8}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/backend/app/utils/server/sync_step.py": {"language": "Python", "code": 41, "comment": 1, "blank": 7}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/backend/app/utils/toolkit/openai_image_toolkit.py": {"language": "Python", "code": 45, "comment": 0, "blank": 6}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/backend/app/utils/toolkit/terminal_toolkit.py": {"language": "Python", "code": 88, "comment": 1, "blank": 11}}