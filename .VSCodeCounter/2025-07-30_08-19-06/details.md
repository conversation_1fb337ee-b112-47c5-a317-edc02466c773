# Details

Date : 2025-07-30 08:19:06

Directory /Users/<USER>/Documents/note/python/todo/eigent-main/backend/app

Total : 62 files,  4354 codes, 1237 comments, 804 blanks, all 6395 lines

[Summary](results.md) / Details / [Diff Summary](diff.md) / [Diff Details](diff-details.md)

## Files
| filename | language | code | comment | blank | total |
| :--- | :--- | ---: | ---: | ---: | ---: |
| [backend/app/\_\_init\_\_.py](/backend/app/__init__.py) | Python | 4 | 0 | 3 | 7 |
| [backend/app/command/\_\_init\_\_.py](/backend/app/command/__init__.py) | Python | 3 | 0 | 3 | 6 |
| [backend/app/component/babel.py](/backend/app/component/babel.py) | Python | 8 | 0 | 3 | 11 |
| [backend/app/component/code.py](/backend/app/component/code.py) | Python | 11 | 0 | 6 | 17 |
| [backend/app/component/command.py](/backend/app/component/command.py) | Python | 5 | 0 | 5 | 10 |
| [backend/app/component/debug.py](/backend/app/component/debug.py) | Python | 10 | 3 | 3 | 16 |
| [backend/app/component/encrypt.py](/backend/app/component/encrypt.py) | Python | 6 | 0 | 6 | 12 |
| [backend/app/component/environment.py](/backend/app/component/environment.py) | Python | 53 | 18 | 28 | 99 |
| [backend/app/component/model\_validation.py](/backend/app/component/model_validation.py) | Python | 9 | 29 | 4 | 42 |
| [backend/app/component/pydantic/i18n.py](/backend/app/component/pydantic/i18n.py) | Python | 34 | 15 | 10 | 59 |
| [backend/app/component/pydantic/translations/en\_US.json](/backend/app/component/pydantic/translations/en_US.json) | JSON | 98 | 0 | 1 | 99 |
| [backend/app/component/pydantic/translations/zh\_CN.json](/backend/app/component/pydantic/translations/zh_CN.json) | JSON | 98 | 0 | 1 | 99 |
| [backend/app/controller/\_\_init\_\_.py](/backend/app/controller/__init__.py) | Python | 0 | 0 | 1 | 1 |
| [backend/app/controller/chat\_controller.py](/backend/app/controller/chat_controller.py) | Python | 64 | 1 | 19 | 84 |
| [backend/app/controller/model\_controller.py](/backend/app/controller/model_controller.py) | Python | 35 | 0 | 9 | 44 |
| [backend/app/controller/task\_controller.py](/backend/app/controller/task_controller.py) | Python | 40 | 0 | 13 | 53 |
| [backend/app/controller/tool\_controller.py](/backend/app/controller/tool_controller.py) | Python | 13 | 0 | 6 | 19 |
| [backend/app/exception/exception.py](/backend/app/exception/exception.py) | Python | 14 | 0 | 7 | 21 |
| [backend/app/exception/handler.py](/backend/app/exception/handler.py) | Python | 37 | 0 | 11 | 48 |
| [backend/app/middleware/\_\_init\_\_.py](/backend/app/middleware/__init__.py) | Python | 4 | 0 | 3 | 7 |
| [backend/app/model/chat.py](/backend/app/model/chat.py) | Python | 83 | 1 | 27 | 111 |
| [backend/app/service/chat\_service.py](/backend/app/service/chat_service.py) | Python | 290 | 172 | 24 | 486 |
| [backend/app/service/task.py](/backend/app/service/task.py) | Python | 261 | 9 | 91 | 361 |
| [backend/app/utils/agent.py](/backend/app/utils/agent.py) | Python | 865 | 358 | 105 | 1,328 |
| [backend/app/utils/listen/toolkit\_listen.py](/backend/app/utils/listen/toolkit_listen.py) | Python | 152 | 7 | 19 | 178 |
| [backend/app/utils/server/sync\_step.py](/backend/app/utils/server/sync_step.py) | Python | 41 | 1 | 7 | 49 |
| [backend/app/utils/single\_agent\_worker.py](/backend/app/utils/single_agent_worker.py) | Python | 43 | 148 | 7 | 198 |
| [backend/app/utils/toolkit/abstract\_toolkit.py](/backend/app/utils/toolkit/abstract_toolkit.py) | Python | 11 | 1 | 5 | 17 |
| [backend/app/utils/toolkit/audio\_analysis\_toolkit.py](/backend/app/utils/toolkit/audio_analysis_toolkit.py) | Python | 30 | 0 | 7 | 37 |
| [backend/app/utils/toolkit/code\_execution\_toolkit.py](/backend/app/utils/toolkit/code_execution_toolkit.py) | Python | 33 | 0 | 7 | 40 |
| [backend/app/utils/toolkit/craw4ai\_toolkit.py](/backend/app/utils/toolkit/craw4ai_toolkit.py) | Python | 14 | 7 | 9 | 30 |
| [backend/app/utils/toolkit/excel\_toolkit.py](/backend/app/utils/toolkit/excel_toolkit.py) | Python | 21 | 0 | 6 | 27 |
| [backend/app/utils/toolkit/file\_write\_toolkit.py](/backend/app/utils/toolkit/file_write_toolkit.py) | Python | 52 | 0 | 5 | 57 |
| [backend/app/utils/toolkit/github\_toolkit.py](/backend/app/utils/toolkit/github_toolkit.py) | Python | 95 | 0 | 13 | 108 |
| [backend/app/utils/toolkit/google\_calendar\_toolkit.py](/backend/app/utils/toolkit/google_calendar_toolkit.py) | Python | 50 | 0 | 10 | 60 |
| [backend/app/utils/toolkit/google\_drive\_mcp\_toolkit.py](/backend/app/utils/toolkit/google_drive_mcp_toolkit.py) | Python | 41 | 0 | 5 | 46 |
| [backend/app/utils/toolkit/google\_gmail\_mcp\_toolkit.py](/backend/app/utils/toolkit/google_gmail_mcp_toolkit.py) | Python | 46 | 0 | 8 | 54 |
| [backend/app/utils/toolkit/human\_toolkit.py](/backend/app/utils/toolkit/human_toolkit.py) | Python | 61 | 70 | 8 | 139 |
| [backend/app/utils/toolkit/hybrid\_browser\_python\_toolkit.py](/backend/app/utils/toolkit/hybrid_browser_python_toolkit.py) | Python | 176 | 193 | 36 | 405 |
| [backend/app/utils/toolkit/hybrid\_browser\_toolkit.py](/backend/app/utils/toolkit/hybrid_browser_toolkit.py) | Python | 215 | 9 | 37 | 261 |
| [backend/app/utils/toolkit/image\_analysis\_toolkit.py](/backend/app/utils/toolkit/image_analysis_toolkit.py) | Python | 34 | 0 | 7 | 41 |
| [backend/app/utils/toolkit/linkedin\_toolkit.py](/backend/app/utils/toolkit/linkedin_toolkit.py) | Python | 35 | 0 | 8 | 43 |
| [backend/app/utils/toolkit/markitdown\_toolkit.py](/backend/app/utils/toolkit/markitdown_toolkit.py) | Python | 13 | 0 | 6 | 19 |
| [backend/app/utils/toolkit/mcp\_search\_toolkit.py](/backend/app/utils/toolkit/mcp_search_toolkit.py) | Python | 44 | 10 | 6 | 60 |
| [backend/app/utils/toolkit/note\_taking\_toolkit.py](/backend/app/utils/toolkit/note_taking_toolkit.py) | Python | 33 | 0 | 9 | 42 |
| [backend/app/utils/toolkit/notion\_mcp\_toolkit.py](/backend/app/utils/toolkit/notion_mcp_toolkit.py) | Python | 42 | 0 | 5 | 47 |
| [backend/app/utils/toolkit/notion\_toolkit.py](/backend/app/utils/toolkit/notion_toolkit.py) | Python | 43 | 0 | 8 | 51 |
| [backend/app/utils/toolkit/openai\_image\_toolkit.py](/backend/app/utils/toolkit/openai_image_toolkit.py) | Python | 45 | 0 | 6 | 51 |
| [backend/app/utils/toolkit/pptx\_toolkit.py](/backend/app/utils/toolkit/pptx_toolkit.py) | Python | 38 | 0 | 7 | 45 |
| [backend/app/utils/toolkit/pyautogui\_toolkit.py](/backend/app/utils/toolkit/pyautogui_toolkit.py) | Python | 76 | 0 | 14 | 90 |
| [backend/app/utils/toolkit/reddit\_toolkit.py](/backend/app/utils/toolkit/reddit_toolkit.py) | Python | 62 | 0 | 8 | 70 |
| [backend/app/utils/toolkit/screenshot\_toolkit.py](/backend/app/utils/toolkit/screenshot_toolkit.py) | Python | 21 | 0 | 7 | 28 |
| [backend/app/utils/toolkit/search\_toolkit.py](/backend/app/utils/toolkit/search_toolkit.py) | Python | 120 | 157 | 25 | 302 |
| [backend/app/utils/toolkit/slack\_toolkit.py](/backend/app/utils/toolkit/slack_toolkit.py) | Python | 66 | 0 | 12 | 78 |
| [backend/app/utils/toolkit/terminal\_toolkit.py](/backend/app/utils/toolkit/terminal_toolkit.py) | Python | 88 | 1 | 11 | 100 |
| [backend/app/utils/toolkit/thinking\_toolkit.py](/backend/app/utils/toolkit/thinking_toolkit.py) | Python | 29 | 0 | 12 | 41 |
| [backend/app/utils/toolkit/twitter\_toolkit.py](/backend/app/utils/toolkit/twitter_toolkit.py) | Python | 65 | 0 | 11 | 76 |
| [backend/app/utils/toolkit/video\_analysis\_toolkit.py](/backend/app/utils/toolkit/video_analysis_toolkit.py) | Python | 40 | 0 | 6 | 46 |
| [backend/app/utils/toolkit/video\_download\_toolkit.py](/backend/app/utils/toolkit/video_download_toolkit.py) | Python | 38 | 0 | 8 | 46 |
| [backend/app/utils/toolkit/web\_deploy\_toolkit.py](/backend/app/utils/toolkit/web_deploy_toolkit.py) | Python | 45 | 0 | 9 | 54 |
| [backend/app/utils/toolkit/whatsapp\_toolkit.py](/backend/app/utils/toolkit/whatsapp_toolkit.py) | Python | 39 | 0 | 8 | 47 |
| [backend/app/utils/workforce.py](/backend/app/utils/workforce.py) | Python | 212 | 27 | 33 | 272 |

[Summary](results.md) / Details / [Diff Summary](diff.md) / [Diff Details](diff-details.md)