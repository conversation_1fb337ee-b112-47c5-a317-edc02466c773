# Summary

Date : 2025-07-30 08:19:06

Directory /Users/<USER>/Documents/note/python/todo/eigent-main/backend/app

Total : 62 files,  4354 codes, 1237 comments, 804 blanks, all 6395 lines

Summary / [Details](details.md) / [Diff Summary](diff.md) / [Diff Details](diff-details.md)

## Languages
| language | files | code | comment | blank | total |
| :--- | ---: | ---: | ---: | ---: | ---: |
| Python | 60 | 4,158 | 1,237 | 802 | 6,197 |
| JSON | 2 | 196 | 0 | 2 | 198 |

## Directories
| path | files | code | comment | blank | total |
| :--- | ---: | ---: | ---: | ---: | ---: |
| . | 62 | 4,354 | 1,237 | 804 | 6,395 |
| . (Files) | 1 | 4 | 0 | 3 | 7 |
| command | 1 | 3 | 0 | 3 | 6 |
| component | 10 | 332 | 65 | 67 | 464 |
| component (Files) | 7 | 102 | 50 | 55 | 207 |
| component/pydantic | 3 | 230 | 15 | 12 | 257 |
| component/pydantic (Files) | 1 | 34 | 15 | 10 | 59 |
| component/pydantic/translations | 2 | 196 | 0 | 2 | 198 |
| controller | 5 | 152 | 1 | 48 | 201 |
| exception | 2 | 51 | 0 | 18 | 69 |
| middleware | 1 | 4 | 0 | 3 | 7 |
| model | 1 | 83 | 1 | 27 | 111 |
| service | 2 | 551 | 181 | 115 | 847 |
| utils | 39 | 3,174 | 989 | 520 | 4,683 |
| utils (Files) | 3 | 1,120 | 533 | 145 | 1,798 |
| utils/listen | 1 | 152 | 7 | 19 | 178 |
| utils/server | 1 | 41 | 1 | 7 | 49 |
| utils/toolkit | 34 | 1,861 | 448 | 349 | 2,658 |

Summary / [Details](details.md) / [Diff Summary](diff.md) / [Diff Details](diff-details.md)