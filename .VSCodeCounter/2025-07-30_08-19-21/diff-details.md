# Diff Details

Date : 2025-07-30 08:19:21

Directory /Users/<USER>/Documents/note/python/todo/eigent-main/src

Total : 202 files,  14312 codes, -567 comments, 559 blanks, all 14304 lines

[Summary](results.md) / [Details](details.md) / [Diff Summary](diff.md) / Diff Details

## Files
| filename | language | code | comment | blank | total |
| :--- | :--- | ---: | ---: | ---: | ---: |
| [backend/app/\_\_init\_\_.py](/backend/app/__init__.py) | Python | -4 | 0 | -3 | -7 |
| [backend/app/command/\_\_init\_\_.py](/backend/app/command/__init__.py) | Python | -3 | 0 | -3 | -6 |
| [backend/app/component/babel.py](/backend/app/component/babel.py) | Python | -8 | 0 | -3 | -11 |
| [backend/app/component/code.py](/backend/app/component/code.py) | Python | -11 | 0 | -6 | -17 |
| [backend/app/component/command.py](/backend/app/component/command.py) | Python | -5 | 0 | -5 | -10 |
| [backend/app/component/debug.py](/backend/app/component/debug.py) | Python | -10 | -3 | -3 | -16 |
| [backend/app/component/encrypt.py](/backend/app/component/encrypt.py) | Python | -6 | 0 | -6 | -12 |
| [backend/app/component/environment.py](/backend/app/component/environment.py) | Python | -53 | -18 | -28 | -99 |
| [backend/app/component/model\_validation.py](/backend/app/component/model_validation.py) | Python | -9 | -29 | -4 | -42 |
| [backend/app/component/pydantic/i18n.py](/backend/app/component/pydantic/i18n.py) | Python | -34 | -15 | -10 | -59 |
| [backend/app/component/pydantic/translations/en\_US.json](/backend/app/component/pydantic/translations/en_US.json) | JSON | -98 | 0 | -1 | -99 |
| [backend/app/component/pydantic/translations/zh\_CN.json](/backend/app/component/pydantic/translations/zh_CN.json) | JSON | -98 | 0 | -1 | -99 |
| [backend/app/controller/\_\_init\_\_.py](/backend/app/controller/__init__.py) | Python | 0 | 0 | -1 | -1 |
| [backend/app/controller/chat\_controller.py](/backend/app/controller/chat_controller.py) | Python | -64 | -1 | -19 | -84 |
| [backend/app/controller/model\_controller.py](/backend/app/controller/model_controller.py) | Python | -35 | 0 | -9 | -44 |
| [backend/app/controller/task\_controller.py](/backend/app/controller/task_controller.py) | Python | -40 | 0 | -13 | -53 |
| [backend/app/controller/tool\_controller.py](/backend/app/controller/tool_controller.py) | Python | -13 | 0 | -6 | -19 |
| [backend/app/exception/exception.py](/backend/app/exception/exception.py) | Python | -14 | 0 | -7 | -21 |
| [backend/app/exception/handler.py](/backend/app/exception/handler.py) | Python | -37 | 0 | -11 | -48 |
| [backend/app/middleware/\_\_init\_\_.py](/backend/app/middleware/__init__.py) | Python | -4 | 0 | -3 | -7 |
| [backend/app/model/chat.py](/backend/app/model/chat.py) | Python | -83 | -1 | -27 | -111 |
| [backend/app/service/chat\_service.py](/backend/app/service/chat_service.py) | Python | -290 | -172 | -24 | -486 |
| [backend/app/service/task.py](/backend/app/service/task.py) | Python | -261 | -9 | -91 | -361 |
| [backend/app/utils/agent.py](/backend/app/utils/agent.py) | Python | -865 | -358 | -105 | -1,328 |
| [backend/app/utils/listen/toolkit\_listen.py](/backend/app/utils/listen/toolkit_listen.py) | Python | -152 | -7 | -19 | -178 |
| [backend/app/utils/server/sync\_step.py](/backend/app/utils/server/sync_step.py) | Python | -41 | -1 | -7 | -49 |
| [backend/app/utils/single\_agent\_worker.py](/backend/app/utils/single_agent_worker.py) | Python | -43 | -148 | -7 | -198 |
| [backend/app/utils/toolkit/abstract\_toolkit.py](/backend/app/utils/toolkit/abstract_toolkit.py) | Python | -11 | -1 | -5 | -17 |
| [backend/app/utils/toolkit/audio\_analysis\_toolkit.py](/backend/app/utils/toolkit/audio_analysis_toolkit.py) | Python | -30 | 0 | -7 | -37 |
| [backend/app/utils/toolkit/code\_execution\_toolkit.py](/backend/app/utils/toolkit/code_execution_toolkit.py) | Python | -33 | 0 | -7 | -40 |
| [backend/app/utils/toolkit/craw4ai\_toolkit.py](/backend/app/utils/toolkit/craw4ai_toolkit.py) | Python | -14 | -7 | -9 | -30 |
| [backend/app/utils/toolkit/excel\_toolkit.py](/backend/app/utils/toolkit/excel_toolkit.py) | Python | -21 | 0 | -6 | -27 |
| [backend/app/utils/toolkit/file\_write\_toolkit.py](/backend/app/utils/toolkit/file_write_toolkit.py) | Python | -52 | 0 | -5 | -57 |
| [backend/app/utils/toolkit/github\_toolkit.py](/backend/app/utils/toolkit/github_toolkit.py) | Python | -95 | 0 | -13 | -108 |
| [backend/app/utils/toolkit/google\_calendar\_toolkit.py](/backend/app/utils/toolkit/google_calendar_toolkit.py) | Python | -50 | 0 | -10 | -60 |
| [backend/app/utils/toolkit/google\_drive\_mcp\_toolkit.py](/backend/app/utils/toolkit/google_drive_mcp_toolkit.py) | Python | -41 | 0 | -5 | -46 |
| [backend/app/utils/toolkit/google\_gmail\_mcp\_toolkit.py](/backend/app/utils/toolkit/google_gmail_mcp_toolkit.py) | Python | -46 | 0 | -8 | -54 |
| [backend/app/utils/toolkit/human\_toolkit.py](/backend/app/utils/toolkit/human_toolkit.py) | Python | -61 | -70 | -8 | -139 |
| [backend/app/utils/toolkit/hybrid\_browser\_python\_toolkit.py](/backend/app/utils/toolkit/hybrid_browser_python_toolkit.py) | Python | -176 | -193 | -36 | -405 |
| [backend/app/utils/toolkit/hybrid\_browser\_toolkit.py](/backend/app/utils/toolkit/hybrid_browser_toolkit.py) | Python | -215 | -9 | -37 | -261 |
| [backend/app/utils/toolkit/image\_analysis\_toolkit.py](/backend/app/utils/toolkit/image_analysis_toolkit.py) | Python | -34 | 0 | -7 | -41 |
| [backend/app/utils/toolkit/linkedin\_toolkit.py](/backend/app/utils/toolkit/linkedin_toolkit.py) | Python | -35 | 0 | -8 | -43 |
| [backend/app/utils/toolkit/markitdown\_toolkit.py](/backend/app/utils/toolkit/markitdown_toolkit.py) | Python | -13 | 0 | -6 | -19 |
| [backend/app/utils/toolkit/mcp\_search\_toolkit.py](/backend/app/utils/toolkit/mcp_search_toolkit.py) | Python | -44 | -10 | -6 | -60 |
| [backend/app/utils/toolkit/note\_taking\_toolkit.py](/backend/app/utils/toolkit/note_taking_toolkit.py) | Python | -33 | 0 | -9 | -42 |
| [backend/app/utils/toolkit/notion\_mcp\_toolkit.py](/backend/app/utils/toolkit/notion_mcp_toolkit.py) | Python | -42 | 0 | -5 | -47 |
| [backend/app/utils/toolkit/notion\_toolkit.py](/backend/app/utils/toolkit/notion_toolkit.py) | Python | -43 | 0 | -8 | -51 |
| [backend/app/utils/toolkit/openai\_image\_toolkit.py](/backend/app/utils/toolkit/openai_image_toolkit.py) | Python | -45 | 0 | -6 | -51 |
| [backend/app/utils/toolkit/pptx\_toolkit.py](/backend/app/utils/toolkit/pptx_toolkit.py) | Python | -38 | 0 | -7 | -45 |
| [backend/app/utils/toolkit/pyautogui\_toolkit.py](/backend/app/utils/toolkit/pyautogui_toolkit.py) | Python | -76 | 0 | -14 | -90 |
| [backend/app/utils/toolkit/reddit\_toolkit.py](/backend/app/utils/toolkit/reddit_toolkit.py) | Python | -62 | 0 | -8 | -70 |
| [backend/app/utils/toolkit/screenshot\_toolkit.py](/backend/app/utils/toolkit/screenshot_toolkit.py) | Python | -21 | 0 | -7 | -28 |
| [backend/app/utils/toolkit/search\_toolkit.py](/backend/app/utils/toolkit/search_toolkit.py) | Python | -120 | -157 | -25 | -302 |
| [backend/app/utils/toolkit/slack\_toolkit.py](/backend/app/utils/toolkit/slack_toolkit.py) | Python | -66 | 0 | -12 | -78 |
| [backend/app/utils/toolkit/terminal\_toolkit.py](/backend/app/utils/toolkit/terminal_toolkit.py) | Python | -88 | -1 | -11 | -100 |
| [backend/app/utils/toolkit/thinking\_toolkit.py](/backend/app/utils/toolkit/thinking_toolkit.py) | Python | -29 | 0 | -12 | -41 |
| [backend/app/utils/toolkit/twitter\_toolkit.py](/backend/app/utils/toolkit/twitter_toolkit.py) | Python | -65 | 0 | -11 | -76 |
| [backend/app/utils/toolkit/video\_analysis\_toolkit.py](/backend/app/utils/toolkit/video_analysis_toolkit.py) | Python | -40 | 0 | -6 | -46 |
| [backend/app/utils/toolkit/video\_download\_toolkit.py](/backend/app/utils/toolkit/video_download_toolkit.py) | Python | -38 | 0 | -8 | -46 |
| [backend/app/utils/toolkit/web\_deploy\_toolkit.py](/backend/app/utils/toolkit/web_deploy_toolkit.py) | Python | -45 | 0 | -9 | -54 |
| [backend/app/utils/toolkit/whatsapp\_toolkit.py](/backend/app/utils/toolkit/whatsapp_toolkit.py) | Python | -39 | 0 | -8 | -47 |
| [backend/app/utils/workforce.py](/backend/app/utils/workforce.py) | Python | -212 | -27 | -33 | -272 |
| [src/App.tsx](/src/App.tsx) | TypeScript JSX | 58 | 2 | 11 | 71 |
| [src/api/http.ts](/src/api/http.ts) | TypeScript | 192 | 13 | 47 | 252 |
| [src/assets/Chrome.svg](/src/assets/Chrome.svg) | XML | 6 | 0 | 1 | 7 |
| [src/assets/Edge.svg](/src/assets/Edge.svg) | XML | 20 | 0 | 1 | 21 |
| [src/assets/Folder-1.svg](/src/assets/Folder-1.svg) | XML | 33 | 0 | 1 | 34 |
| [src/assets/Folder.svg](/src/assets/Folder.svg) | XML | 33 | 0 | 1 | 34 |
| [src/assets/NoIcon.svg](/src/assets/NoIcon.svg) | XML | 6 | 0 | 1 | 7 |
| [src/assets/animation/onboarding\_success.json](/src/assets/animation/onboarding_success.json) | JSON | 1 | 0 | 0 | 1 |
| [src/assets/animation/openning\_animaiton.json](/src/assets/animation/openning_animaiton.json) | JSON | 1 | 0 | 0 | 1 |
| [src/assets/chevron\_left.svg](/src/assets/chevron_left.svg) | XML | 6 | 0 | 1 | 7 |
| [src/assets/eye-off.svg](/src/assets/eye-off.svg) | XML | 3 | 0 | 1 | 4 |
| [src/assets/eye.svg](/src/assets/eye.svg) | XML | 4 | 0 | 1 | 5 |
| [src/assets/github.svg](/src/assets/github.svg) | XML | 8 | 0 | 0 | 8 |
| [src/assets/github2.svg](/src/assets/github2.svg) | XML | 3 | 0 | 1 | 4 |
| [src/assets/google.svg](/src/assets/google.svg) | XML | 1 | 0 | 0 | 1 |
| [src/assets/logo-electron.svg](/src/assets/logo-electron.svg) | XML | 8 | 0 | 1 | 9 |
| [src/assets/logo-v1.svg](/src/assets/logo-v1.svg) | XML | 1 | 0 | 0 | 1 |
| [src/assets/logo-vite.svg](/src/assets/logo-vite.svg) | XML | 20 | 0 | 1 | 21 |
| [src/assets/mcp/Anthropic.svg](/src/assets/mcp/Anthropic.svg) | XML | 13 | 0 | 0 | 13 |
| [src/assets/mcp/Camel.svg](/src/assets/mcp/Camel.svg) | XML | 6 | 0 | 0 | 6 |
| [src/assets/mcp/Community.svg](/src/assets/mcp/Community.svg) | XML | 12 | 0 | 0 | 12 |
| [src/assets/mcp/Ellipse-25.svg](/src/assets/mcp/Ellipse-25.svg) | XML | 3 | 0 | 0 | 3 |
| [src/assets/mcp/Official.svg](/src/assets/mcp/Official.svg) | XML | 8 | 0 | 0 | 8 |
| [src/assets/rac-pause.svg](/src/assets/rac-pause.svg) | XML | 3 | 0 | 1 | 4 |
| [src/components/AddWorker/IntegrationList.tsx](/src/components/AddWorker/IntegrationList.tsx) | TypeScript JSX | 432 | 27 | 19 | 478 |
| [src/components/AddWorker/ToolSelect.tsx](/src/components/AddWorker/ToolSelect.tsx) | TypeScript JSX | 467 | 28 | 34 | 529 |
| [src/components/AddWorker/index.tsx](/src/components/AddWorker/index.tsx) | TypeScript JSX | 445 | 15 | 26 | 486 |
| [src/components/AnimationJson.tsx](/src/components/AnimationJson.tsx) | TypeScript JSX | 32 | 1 | 3 | 36 |
| [src/components/BottomBar/index.tsx](/src/components/BottomBar/index.tsx) | TypeScript JSX | 9 | 0 | 3 | 12 |
| [src/components/ChatBox/BottomInput.tsx](/src/components/ChatBox/BottomInput.tsx) | TypeScript JSX | 438 | 5 | 16 | 459 |
| [src/components/ChatBox/MarkDown.tsx](/src/components/ChatBox/MarkDown.tsx) | TypeScript JSX | 185 | 1 | 7 | 193 |
| [src/components/ChatBox/MessageCard.tsx](/src/components/ChatBox/MessageCard.tsx) | TypeScript JSX | 87 | 5 | 9 | 101 |
| [src/components/ChatBox/NoticeCard.tsx](/src/components/ChatBox/NoticeCard.tsx) | TypeScript JSX | 86 | 2 | 7 | 95 |
| [src/components/ChatBox/SummaryMarkDown.tsx](/src/components/ChatBox/SummaryMarkDown.tsx) | TypeScript JSX | 106 | 0 | 7 | 113 |
| [src/components/ChatBox/TaskCard.tsx](/src/components/ChatBox/TaskCard.tsx) | TypeScript JSX | 283 | 0 | 12 | 295 |
| [src/components/ChatBox/TaskItem.tsx](/src/components/ChatBox/TaskItem.tsx) | TypeScript JSX | 130 | 2 | 6 | 138 |
| [src/components/ChatBox/TaskType.tsx](/src/components/ChatBox/TaskType.tsx) | TypeScript JSX | 30 | 0 | 1 | 31 |
| [src/components/ChatBox/TypeCardSkeleton.tsx](/src/components/ChatBox/TypeCardSkeleton.tsx) | TypeScript JSX | 92 | 0 | 4 | 96 |
| [src/components/ChatBox/index.tsx](/src/components/ChatBox/index.tsx) | TypeScript JSX | 520 | 16 | 18 | 554 |
| [src/components/Folder/index.tsx](/src/components/Folder/index.tsx) | TypeScript JSX | 336 | 8 | 12 | 356 |
| [src/components/GlobalSearch/index.tsx](/src/components/GlobalSearch/index.tsx) | TypeScript JSX | 65 | 0 | 6 | 71 |
| [src/components/HistorySidebar/SearchInput.tsx](/src/components/HistorySidebar/SearchInput.tsx) | TypeScript JSX | 35 | 1 | 5 | 41 |
| [src/components/HistorySidebar/index.tsx](/src/components/HistorySidebar/index.tsx) | TypeScript JSX | 564 | 11 | 24 | 599 |
| [src/components/InstallStep/Carousel.tsx](/src/components/InstallStep/Carousel.tsx) | TypeScript JSX | 162 | 5 | 13 | 180 |
| [src/components/InstallStep/InstallDependencies.tsx](/src/components/InstallStep/InstallDependencies.tsx) | TypeScript JSX | 166 | 14 | 12 | 192 |
| [src/components/InstallStep/Permissions.tsx](/src/components/InstallStep/Permissions.tsx) | TypeScript JSX | 119 | 0 | 6 | 125 |
| [src/components/Layout/index.tsx](/src/components/Layout/index.tsx) | TypeScript JSX | 51 | 1 | 3 | 55 |
| [src/components/SearchAgentWrokSpace/index.tsx](/src/components/SearchAgentWrokSpace/index.tsx) | TypeScript JSX | 360 | 15 | 13 | 388 |
| [src/components/SearchHistoryDialog.tsx](/src/components/SearchHistoryDialog.tsx) | TypeScript JSX | 96 | 2 | 5 | 103 |
| [src/components/SearchInput/index.tsx](/src/components/SearchInput/index.tsx) | TypeScript JSX | 37 | 1 | 5 | 43 |
| [src/components/TaskState/index.tsx](/src/components/TaskState/index.tsx) | TypeScript JSX | 48 | 0 | 1 | 49 |
| [src/components/Terminal/index.tsx](/src/components/Terminal/index.tsx) | TypeScript JSX | 267 | 50 | 50 | 367 |
| [src/components/TerminalAgentWrokSpace/index.tsx](/src/components/TerminalAgentWrokSpace/index.tsx) | TypeScript JSX | 296 | 27 | 6 | 329 |
| [src/components/ThemeProvider.tsx](/src/components/ThemeProvider.tsx) | TypeScript JSX | 33 | 4 | 8 | 45 |
| [src/components/Toast/creditsToast.tsx](/src/components/Toast/creditsToast.tsx) | TypeScript JSX | 25 | 0 | 2 | 27 |
| [src/components/Toast/storageToast.tsx](/src/components/Toast/storageToast.tsx) | TypeScript JSX | 21 | 0 | 2 | 23 |
| [src/components/Toast/trafficToast.tsx](/src/components/Toast/trafficToast.tsx) | TypeScript JSX | 11 | 0 | 2 | 13 |
| [src/components/TopBar/index.css](/src/components/TopBar/index.css) | PostCSS | 26 | 0 | 3 | 29 |
| [src/components/TopBar/index.tsx](/src/components/TopBar/index.tsx) | TypeScript JSX | 198 | 7 | 11 | 216 |
| [src/components/WorkFlow/MarkDown.tsx](/src/components/WorkFlow/MarkDown.tsx) | TypeScript JSX | 176 | 1 | 7 | 184 |
| [src/components/WorkFlow/index.tsx](/src/components/WorkFlow/index.tsx) | TypeScript JSX | 333 | 53 | 26 | 412 |
| [src/components/WorkFlow/node.tsx](/src/components/WorkFlow/node.tsx) | TypeScript JSX | 753 | 2 | 20 | 775 |
| [src/components/WorkSpaceMenu/index.tsx](/src/components/WorkSpaceMenu/index.tsx) | TypeScript JSX | 350 | 10 | 13 | 373 |
| [src/components/ui/ShinyText/ShinyText.css](/src/components/ui/ShinyText/ShinyText.css) | PostCSS | 28 | 0 | 3 | 31 |
| [src/components/ui/ShinyText/ShinyText.tsx](/src/components/ui/ShinyText/ShinyText.tsx) | TypeScript JSX | 19 | 0 | 4 | 23 |
| [src/components/ui/accordion.tsx](/src/components/ui/accordion.tsx) | TypeScript JSX | 49 | 0 | 7 | 56 |
| [src/components/ui/alert.tsx](/src/components/ui/alert.tsx) | TypeScript JSX | 53 | 0 | 7 | 60 |
| [src/components/ui/alertDialog.tsx](/src/components/ui/alertDialog.tsx) | TypeScript JSX | 63 | 2 | 5 | 70 |
| [src/components/ui/badge.tsx](/src/components/ui/badge.tsx) | TypeScript JSX | 31 | 0 | 6 | 37 |
| [src/components/ui/button.tsx](/src/components/ui/button.tsx) | TypeScript JSX | 57 | 1 | 7 | 65 |
| [src/components/ui/card.tsx](/src/components/ui/card.tsx) | TypeScript JSX | 68 | 0 | 9 | 77 |
| [src/components/ui/carousel.tsx](/src/components/ui/carousel.tsx) | TypeScript JSX | 231 | 0 | 30 | 261 |
| [src/components/ui/command.tsx](/src/components/ui/command.tsx) | TypeScript JSX | 135 | 0 | 17 | 152 |
| [src/components/ui/dialog.tsx](/src/components/ui/dialog.tsx) | TypeScript JSX | 109 | 0 | 14 | 123 |
| [src/components/ui/dropdown-menu.tsx](/src/components/ui/dropdown-menu.tsx) | TypeScript JSX | 182 | 0 | 18 | 200 |
| [src/components/ui/input.tsx](/src/components/ui/input.tsx) | TypeScript JSX | 19 | 0 | 4 | 23 |
| [src/components/ui/label.tsx](/src/components/ui/label.tsx) | TypeScript JSX | 20 | 0 | 5 | 25 |
| [src/components/ui/popover.tsx](/src/components/ui/popover.tsx) | TypeScript JSX | 26 | 0 | 8 | 34 |
| [src/components/ui/progress-install.tsx](/src/components/ui/progress-install.tsx) | TypeScript JSX | 27 | 0 | 4 | 31 |
| [src/components/ui/progress.tsx](/src/components/ui/progress.tsx) | TypeScript JSX | 23 | 0 | 4 | 27 |
| [src/components/ui/select.tsx](/src/components/ui/select.tsx) | TypeScript JSX | 145 | 0 | 13 | 158 |
| [src/components/ui/separator.tsx](/src/components/ui/separator.tsx) | TypeScript JSX | 26 | 0 | 4 | 30 |
| [src/components/ui/sheet.tsx](/src/components/ui/sheet.tsx) | TypeScript JSX | 125 | 0 | 16 | 141 |
| [src/components/ui/sidebar.tsx](/src/components/ui/sidebar.tsx) | TypeScript JSX | 706 | 12 | 54 | 772 |
| [src/components/ui/skeleton.tsx](/src/components/ui/skeleton.tsx) | TypeScript JSX | 13 | 0 | 3 | 16 |
| [src/components/ui/sonner.tsx](/src/components/ui/sonner.tsx) | TypeScript JSX | 25 | 0 | 5 | 30 |
| [src/components/ui/switch.tsx](/src/components/ui/switch.tsx) | TypeScript JSX | 24 | 0 | 4 | 28 |
| [src/components/ui/tabs.tsx](/src/components/ui/tabs.tsx) | TypeScript JSX | 47 | 0 | 7 | 54 |
| [src/components/ui/tag.tsx](/src/components/ui/tag.tsx) | TypeScript JSX | 45 | 4 | 8 | 57 |
| [src/components/ui/textarea.tsx](/src/components/ui/textarea.tsx) | TypeScript JSX | 19 | 0 | 4 | 23 |
| [src/components/ui/toggle-group.tsx](/src/components/ui/toggle-group.tsx) | TypeScript JSX | 51 | 0 | 9 | 60 |
| [src/components/ui/toggle.tsx](/src/components/ui/toggle.tsx) | TypeScript JSX | 39 | 0 | 7 | 46 |
| [src/components/ui/tooltip.tsx](/src/components/ui/tooltip.tsx) | TypeScript JSX | 24 | 0 | 7 | 31 |
| [src/components/update/index.tsx](/src/components/update/index.tsx) | TypeScript JSX | 86 | 0 | 13 | 99 |
| [src/demos/node.ts](/src/demos/node.ts) | TypeScript | 7 | 0 | 2 | 9 |
| [src/hooks/use-app-version.tsx](/src/hooks/use-app-version.tsx) | TypeScript JSX | 11 | 0 | 4 | 15 |
| [src/hooks/use-mobile.tsx](/src/hooks/use-mobile.tsx) | TypeScript JSX | 15 | 0 | 5 | 20 |
| [src/lib/index.ts](/src/lib/index.ts) | TypeScript | 43 | 0 | 10 | 53 |
| [src/lib/llm.ts](/src/lib/llm.ts) | TypeScript | 85 | 0 | 1 | 86 |
| [src/lib/oauth.ts](/src/lib/oauth.ts) | TypeScript | 190 | 4 | 32 | 226 |
| [src/lib/share.ts](/src/lib/share.ts) | TypeScript | 20 | 0 | 1 | 21 |
| [src/lib/utils.ts](/src/lib/utils.ts) | TypeScript | 5 | 0 | 2 | 7 |
| [src/main.tsx](/src/main.tsx) | TypeScript JSX | 24 | 4 | 5 | 33 |
| [src/pages/History.tsx](/src/pages/History.tsx) | TypeScript JSX | 654 | 8 | 25 | 687 |
| [src/pages/Home.tsx](/src/pages/Home.tsx) | TypeScript JSX | 240 | 21 | 15 | 276 |
| [src/pages/Login.tsx](/src/pages/Login.tsx) | TypeScript JSX | 312 | 3 | 29 | 344 |
| [src/pages/NotFound.tsx](/src/pages/NotFound.tsx) | TypeScript JSX | 4 | 0 | 2 | 6 |
| [src/pages/Setting.tsx](/src/pages/Setting.tsx) | TypeScript JSX | 127 | 6 | 10 | 143 |
| [src/pages/Setting/API.tsx](/src/pages/Setting/API.tsx) | TypeScript JSX | 102 | 0 | 7 | 109 |
| [src/pages/Setting/General.tsx](/src/pages/Setting/General.tsx) | TypeScript JSX | 135 | 32 | 8 | 175 |
| [src/pages/Setting/MCP.tsx](/src/pages/Setting/MCP.tsx) | TypeScript JSX | 353 | 8 | 19 | 380 |
| [src/pages/Setting/MCPMarket.tsx](/src/pages/Setting/MCPMarket.tsx) | TypeScript JSX | 409 | 15 | 18 | 442 |
| [src/pages/Setting/Models.tsx](/src/pages/Setting/Models.tsx) | TypeScript JSX | 873 | 21 | 19 | 913 |
| [src/pages/Setting/Privacy.tsx](/src/pages/Setting/Privacy.tsx) | TypeScript JSX | 91 | 0 | 7 | 98 |
| [src/pages/Setting/components/IntegrationList.tsx](/src/pages/Setting/components/IntegrationList.tsx) | TypeScript JSX | 391 | 22 | 27 | 440 |
| [src/pages/Setting/components/MCPAddDialog.tsx](/src/pages/Setting/components/MCPAddDialog.tsx) | TypeScript JSX | 147 | 4 | 7 | 158 |
| [src/pages/Setting/components/MCPConfigDialog.tsx](/src/pages/Setting/components/MCPConfigDialog.tsx) | TypeScript JSX | 97 | 0 | 3 | 100 |
| [src/pages/Setting/components/MCPDeleteDialog.tsx](/src/pages/Setting/components/MCPDeleteDialog.tsx) | TypeScript JSX | 26 | 0 | 2 | 28 |
| [src/pages/Setting/components/MCPEnvDialog.tsx](/src/pages/Setting/components/MCPEnvDialog.tsx) | TypeScript JSX | 213 | 0 | 12 | 225 |
| [src/pages/Setting/components/MCPList.tsx](/src/pages/Setting/components/MCPList.tsx) | TypeScript JSX | 26 | 0 | 2 | 28 |
| [src/pages/Setting/components/MCPListItem.tsx](/src/pages/Setting/components/MCPListItem.tsx) | TypeScript JSX | 103 | 0 | 4 | 107 |
| [src/pages/Setting/components/types.ts](/src/pages/Setting/components/types.ts) | TypeScript | 20 | 0 | 1 | 21 |
| [src/pages/Setting/components/utils.ts](/src/pages/Setting/components/utils.ts) | TypeScript | 10 | 0 | 1 | 11 |
| [src/pages/SignUp.tsx](/src/pages/SignUp.tsx) | TypeScript JSX | 345 | 3 | 29 | 377 |
| [src/pages/Task.tsx](/src/pages/Task.tsx) | TypeScript JSX | 3 | 0 | 2 | 5 |
| [src/routers/index.tsx](/src/routers/index.tsx) | TypeScript JSX | 60 | 4 | 7 | 71 |
| [src/stack/client.ts](/src/stack/client.ts) | TypeScript | 13 | 0 | 2 | 15 |
| [src/store/authStore.ts](/src/store/authStore.ts) | TypeScript | 128 | 19 | 28 | 175 |
| [src/store/chatStore.ts](/src/store/chatStore.ts) | TypeScript | 1,435 | 92 | 102 | 1,629 |
| [src/store/globalStore.ts](/src/store/globalStore.ts) | TypeScript | 25 | 4 | 4 | 33 |
| [src/store/sidebarStore.ts](/src/store/sidebarStore.ts) | TypeScript | 13 | 0 | 2 | 15 |
| [src/style/index.css](/src/style/index.css) | PostCSS | 271 | 6 | 53 | 330 |
| [src/style/token.css](/src/style/token.css) | PostCSS | 576 | 42 | 25 | 643 |
| [src/types/chatbox.d.ts](/src/types/chatbox.d.ts) | TypeScript | 118 | 1 | 13 | 132 |
| [src/types/electron-updater.d.ts](/src/types/electron-updater.d.ts) | TypeScript | 9 | 0 | 2 | 11 |
| [src/types/electron.d.ts](/src/types/electron.d.ts) | TypeScript | 65 | 0 | 3 | 68 |
| [src/types/index.ts](/src/types/index.ts) | TypeScript | 29 | 0 | 2 | 31 |
| [src/types/stackframe-react.d.ts](/src/types/stackframe-react.d.ts) | TypeScript | 1 | 0 | 1 | 2 |
| [src/types/workspace.d.ts](/src/types/workspace.d.ts) | TypeScript | 4 | 1 | 3 | 8 |
| [src/vite-env.d.ts](/src/vite-env.d.ts) | TypeScript | 4 | 2 | 2 | 8 |

[Summary](results.md) / [Details](details.md) / [Diff Summary](diff.md) / Diff Details