{"file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/store/authStore.ts": {"language": "TypeScript", "code": 128, "comment": 19, "blank": 28}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/store/globalStore.ts": {"language": "TypeScript", "code": 25, "comment": 4, "blank": 4}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/store/chatStore.ts": {"language": "TypeScript", "code": 1435, "comment": 92, "blank": 102}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/store/sidebarStore.ts": {"language": "TypeScript", "code": 13, "comment": 0, "blank": 2}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/pages/NotFound.tsx": {"language": "TypeScript JSX", "code": 4, "comment": 0, "blank": 2}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/demos/node.ts": {"language": "TypeScript", "code": 7, "comment": 0, "blank": 2}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/pages/Setting/MCPMarket.tsx": {"language": "TypeScript JSX", "code": 409, "comment": 15, "blank": 18}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/pages/Setting/General.tsx": {"language": "TypeScript JSX", "code": 135, "comment": 32, "blank": 8}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/pages/Setting/API.tsx": {"language": "TypeScript JSX", "code": 102, "comment": 0, "blank": 7}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/pages/Setting/Privacy.tsx": {"language": "TypeScript JSX", "code": 91, "comment": 0, "blank": 7}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/pages/Setting/MCP.tsx": {"language": "TypeScript JSX", "code": 353, "comment": 8, "blank": 19}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/pages/Setting.tsx": {"language": "TypeScript JSX", "code": 127, "comment": 6, "blank": 10}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/pages/Setting/Models.tsx": {"language": "TypeScript JSX", "code": 873, "comment": 21, "blank": 19}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/pages/SignUp.tsx": {"language": "TypeScript JSX", "code": 345, "comment": 3, "blank": 29}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/pages/Task.tsx": {"language": "TypeScript JSX", "code": 3, "comment": 0, "blank": 2}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/pages/History.tsx": {"language": "TypeScript JSX", "code": 654, "comment": 8, "blank": 25}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/pages/Home.tsx": {"language": "TypeScript JSX", "code": 240, "comment": 21, "blank": 15}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/pages/Setting/components/IntegrationList.tsx": {"language": "TypeScript JSX", "code": 391, "comment": 22, "blank": 27}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/pages/Setting/components/utils.ts": {"language": "TypeScript", "code": 10, "comment": 0, "blank": 1}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/pages/Login.tsx": {"language": "TypeScript JSX", "code": 312, "comment": 3, "blank": 29}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/pages/Setting/components/MCPConfigDialog.tsx": {"language": "TypeScript JSX", "code": 97, "comment": 0, "blank": 3}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/pages/Setting/components/MCPDeleteDialog.tsx": {"language": "TypeScript JSX", "code": 26, "comment": 0, "blank": 2}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/pages/Setting/components/MCPAddDialog.tsx": {"language": "TypeScript JSX", "code": 147, "comment": 4, "blank": 7}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/pages/Setting/components/MCPList.tsx": {"language": "TypeScript JSX", "code": 26, "comment": 0, "blank": 2}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/pages/Setting/components/MCPEnvDialog.tsx": {"language": "TypeScript JSX", "code": 213, "comment": 0, "blank": 12}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/pages/Setting/components/MCPListItem.tsx": {"language": "TypeScript JSX", "code": 103, "comment": 0, "blank": 4}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/pages/Setting/components/types.ts": {"language": "TypeScript", "code": 20, "comment": 0, "blank": 1}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/api/http.ts": {"language": "TypeScript", "code": 192, "comment": 13, "blank": 47}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/lib/oauth.ts": {"language": "TypeScript", "code": 190, "comment": 4, "blank": 32}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/vite-env.d.ts": {"language": "TypeScript", "code": 4, "comment": 2, "blank": 2}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/style/index.css": {"language": "PostCSS", "code": 271, "comment": 6, "blank": 53}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/types/stackframe-react.d.ts": {"language": "TypeScript", "code": 1, "comment": 0, "blank": 1}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/assets/rac-pause.svg": {"language": "XML", "code": 3, "comment": 0, "blank": 1}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/assets/logo-vite.svg": {"language": "XML", "code": 20, "comment": 0, "blank": 1}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/lib/llm.ts": {"language": "TypeScript", "code": 85, "comment": 0, "blank": 1}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/components/SearchHistoryDialog.tsx": {"language": "TypeScript JSX", "code": 96, "comment": 2, "blank": 5}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/lib/index.ts": {"language": "TypeScript", "code": 43, "comment": 0, "blank": 10}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/types/electron-updater.d.ts": {"language": "TypeScript", "code": 9, "comment": 0, "blank": 2}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/components/Layout/index.tsx": {"language": "TypeScript JSX", "code": 51, "comment": 1, "blank": 3}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/lib/utils.ts": {"language": "TypeScript", "code": 5, "comment": 0, "blank": 2}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/components/TerminalAgentWrokSpace/index.tsx": {"language": "TypeScript JSX", "code": 296, "comment": 27, "blank": 6}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/components/AnimationJson.tsx": {"language": "TypeScript JSX", "code": 32, "comment": 1, "blank": 3}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/assets/chevron_left.svg": {"language": "XML", "code": 6, "comment": 0, "blank": 1}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/assets/Edge.svg": {"language": "XML", "code": 20, "comment": 0, "blank": 1}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/lib/share.ts": {"language": "TypeScript", "code": 20, "comment": 0, "blank": 1}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/types/electron.d.ts": {"language": "TypeScript", "code": 65, "comment": 0, "blank": 3}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/types/index.ts": {"language": "TypeScript", "code": 29, "comment": 0, "blank": 2}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/style/token.css": {"language": "PostCSS", "code": 576, "comment": 42, "blank": 25}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/assets/NoIcon.svg": {"language": "XML", "code": 6, "comment": 0, "blank": 1}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/components/WorkFlow/node.tsx": {"language": "TypeScript JSX", "code": 753, "comment": 2, "blank": 20}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/assets/logo-electron.svg": {"language": "XML", "code": 8, "comment": 0, "blank": 1}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/components/BottomBar/index.tsx": {"language": "TypeScript JSX", "code": 9, "comment": 0, "blank": 3}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/components/WorkFlow/MarkDown.tsx": {"language": "TypeScript JSX", "code": 176, "comment": 1, "blank": 7}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/components/WorkFlow/index.tsx": {"language": "TypeScript JSX", "code": 333, "comment": 53, "blank": 26}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/assets/google.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/assets/eye-off.svg": {"language": "XML", "code": 3, "comment": 0, "blank": 1}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/assets/animation/onboarding_success.json": {"language": "JSON", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/components/GlobalSearch/index.tsx": {"language": "TypeScript JSX", "code": 65, "comment": 0, "blank": 6}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/components/ThemeProvider.tsx": {"language": "TypeScript JSX", "code": 33, "comment": 4, "blank": 8}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/assets/github2.svg": {"language": "XML", "code": 3, "comment": 0, "blank": 1}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/assets/animation/openning_animaiton.json": {"language": "JSON", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/hooks/use-app-version.tsx": {"language": "TypeScript JSX", "code": 11, "comment": 0, "blank": 4}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/assets/eye.svg": {"language": "XML", "code": 4, "comment": 0, "blank": 1}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/components/ChatBox/TaskCard.tsx": {"language": "TypeScript JSX", "code": 283, "comment": 0, "blank": 12}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/components/ChatBox/TypeCardSkeleton.tsx": {"language": "TypeScript JSX", "code": 92, "comment": 0, "blank": 4}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/components/AddWorker/IntegrationList.tsx": {"language": "TypeScript JSX", "code": 432, "comment": 27, "blank": 19}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/assets/Folder-1.svg": {"language": "XML", "code": 33, "comment": 0, "blank": 1}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/components/ChatBox/index.tsx": {"language": "TypeScript JSX", "code": 520, "comment": 16, "blank": 18}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/components/AddWorker/ToolSelect.tsx": {"language": "TypeScript JSX", "code": 467, "comment": 28, "blank": 34}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/components/AddWorker/index.tsx": {"language": "TypeScript JSX", "code": 445, "comment": 15, "blank": 26}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/components/ChatBox/TaskType.tsx": {"language": "TypeScript JSX", "code": 30, "comment": 0, "blank": 1}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/components/ChatBox/NoticeCard.tsx": {"language": "TypeScript JSX", "code": 86, "comment": 2, "blank": 7}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/assets/Chrome.svg": {"language": "XML", "code": 6, "comment": 0, "blank": 1}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/components/ChatBox/MarkDown.tsx": {"language": "TypeScript JSX", "code": 185, "comment": 1, "blank": 7}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/types/chatbox.d.ts": {"language": "TypeScript", "code": 118, "comment": 1, "blank": 13}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/assets/logo-v1.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/assets/Folder.svg": {"language": "XML", "code": 33, "comment": 0, "blank": 1}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/hooks/use-mobile.tsx": {"language": "TypeScript JSX", "code": 15, "comment": 0, "blank": 5}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/types/workspace.d.ts": {"language": "TypeScript", "code": 4, "comment": 1, "blank": 3}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/stack/client.ts": {"language": "TypeScript", "code": 13, "comment": 0, "blank": 2}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/components/ChatBox/SummaryMarkDown.tsx": {"language": "TypeScript JSX", "code": 106, "comment": 0, "blank": 7}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/components/ChatBox/TaskItem.tsx": {"language": "TypeScript JSX", "code": 130, "comment": 2, "blank": 6}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/assets/github.svg": {"language": "XML", "code": 8, "comment": 0, "blank": 0}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/components/SearchAgentWrokSpace/index.tsx": {"language": "TypeScript JSX", "code": 360, "comment": 15, "blank": 13}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/components/ChatBox/MessageCard.tsx": {"language": "TypeScript JSX", "code": 87, "comment": 5, "blank": 9}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/components/Terminal/index.tsx": {"language": "TypeScript JSX", "code": 267, "comment": 50, "blank": 50}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/components/ChatBox/BottomInput.tsx": {"language": "TypeScript JSX", "code": 438, "comment": 5, "blank": 16}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/main.tsx": {"language": "TypeScript JSX", "code": 24, "comment": 4, "blank": 5}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/App.tsx": {"language": "TypeScript JSX", "code": 58, "comment": 2, "blank": 11}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/components/Folder/index.tsx": {"language": "TypeScript JSX", "code": 336, "comment": 8, "blank": 12}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/components/WorkSpaceMenu/index.tsx": {"language": "TypeScript JSX", "code": 350, "comment": 10, "blank": 13}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/components/HistorySidebar/SearchInput.tsx": {"language": "TypeScript JSX", "code": 35, "comment": 1, "blank": 5}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/components/Toast/trafficToast.tsx": {"language": "TypeScript JSX", "code": 11, "comment": 0, "blank": 2}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/components/update/index.tsx": {"language": "TypeScript JSX", "code": 86, "comment": 0, "blank": 13}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/components/Toast/storageToast.tsx": {"language": "TypeScript JSX", "code": 21, "comment": 0, "blank": 2}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/routers/index.tsx": {"language": "TypeScript JSX", "code": 60, "comment": 4, "blank": 7}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/components/Toast/creditsToast.tsx": {"language": "TypeScript JSX", "code": 25, "comment": 0, "blank": 2}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/components/HistorySidebar/index.tsx": {"language": "TypeScript JSX", "code": 564, "comment": 11, "blank": 24}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/components/TopBar/index.css": {"language": "PostCSS", "code": 26, "comment": 0, "blank": 3}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/components/InstallStep/Carousel.tsx": {"language": "TypeScript JSX", "code": 162, "comment": 5, "blank": 13}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/components/InstallStep/InstallDependencies.tsx": {"language": "TypeScript JSX", "code": 166, "comment": 14, "blank": 12}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/components/InstallStep/Permissions.tsx": {"language": "TypeScript JSX", "code": 119, "comment": 0, "blank": 6}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/components/ui/popover.tsx": {"language": "TypeScript JSX", "code": 26, "comment": 0, "blank": 8}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/components/ui/card.tsx": {"language": "TypeScript JSX", "code": 68, "comment": 0, "blank": 9}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/components/ui/tabs.tsx": {"language": "TypeScript JSX", "code": 47, "comment": 0, "blank": 7}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/components/TopBar/index.tsx": {"language": "TypeScript JSX", "code": 198, "comment": 7, "blank": 11}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/components/SearchInput/index.tsx": {"language": "TypeScript JSX", "code": 37, "comment": 1, "blank": 5}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/components/TaskState/index.tsx": {"language": "TypeScript JSX", "code": 48, "comment": 0, "blank": 1}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/components/ui/progress.tsx": {"language": "TypeScript JSX", "code": 23, "comment": 0, "blank": 4}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/components/ui/badge.tsx": {"language": "TypeScript JSX", "code": 31, "comment": 0, "blank": 6}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/components/ui/label.tsx": {"language": "TypeScript JSX", "code": 20, "comment": 0, "blank": 5}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/components/ui/sheet.tsx": {"language": "TypeScript JSX", "code": 125, "comment": 0, "blank": 16}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/components/ui/toggle.tsx": {"language": "TypeScript JSX", "code": 39, "comment": 0, "blank": 7}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/components/ui/dialog.tsx": {"language": "TypeScript JSX", "code": 109, "comment": 0, "blank": 14}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/components/ui/sidebar.tsx": {"language": "TypeScript JSX", "code": 706, "comment": 12, "blank": 54}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/components/ui/progress-install.tsx": {"language": "TypeScript JSX", "code": 27, "comment": 0, "blank": 4}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/components/ui/dropdown-menu.tsx": {"language": "TypeScript JSX", "code": 182, "comment": 0, "blank": 18}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/assets/mcp/Community.svg": {"language": "XML", "code": 12, "comment": 0, "blank": 0}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/components/ui/alertDialog.tsx": {"language": "TypeScript JSX", "code": 63, "comment": 2, "blank": 5}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/components/ui/separator.tsx": {"language": "TypeScript JSX", "code": 26, "comment": 0, "blank": 4}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/components/ui/button.tsx": {"language": "TypeScript JSX", "code": 57, "comment": 1, "blank": 7}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/components/ui/carousel.tsx": {"language": "TypeScript JSX", "code": 231, "comment": 0, "blank": 30}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/components/ui/input.tsx": {"language": "TypeScript JSX", "code": 19, "comment": 0, "blank": 4}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/components/ui/textarea.tsx": {"language": "TypeScript JSX", "code": 19, "comment": 0, "blank": 4}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/assets/mcp/Camel.svg": {"language": "XML", "code": 6, "comment": 0, "blank": 0}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/assets/mcp/Official.svg": {"language": "XML", "code": 8, "comment": 0, "blank": 0}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/assets/mcp/Anthropic.svg": {"language": "XML", "code": 13, "comment": 0, "blank": 0}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/components/ui/skeleton.tsx": {"language": "TypeScript JSX", "code": 13, "comment": 0, "blank": 3}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/assets/mcp/Ellipse-25.svg": {"language": "XML", "code": 3, "comment": 0, "blank": 0}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/components/ui/select.tsx": {"language": "TypeScript JSX", "code": 145, "comment": 0, "blank": 13}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/components/ui/toggle-group.tsx": {"language": "TypeScript JSX", "code": 51, "comment": 0, "blank": 9}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/components/ui/command.tsx": {"language": "TypeScript JSX", "code": 135, "comment": 0, "blank": 17}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/components/ui/switch.tsx": {"language": "TypeScript JSX", "code": 24, "comment": 0, "blank": 4}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/components/ui/alert.tsx": {"language": "TypeScript JSX", "code": 53, "comment": 0, "blank": 7}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/components/ui/tooltip.tsx": {"language": "TypeScript JSX", "code": 24, "comment": 0, "blank": 7}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/components/ui/tag.tsx": {"language": "TypeScript JSX", "code": 45, "comment": 4, "blank": 8}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/components/ui/accordion.tsx": {"language": "TypeScript JSX", "code": 49, "comment": 0, "blank": 7}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/components/ui/sonner.tsx": {"language": "TypeScript JSX", "code": 25, "comment": 0, "blank": 5}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/components/ui/ShinyText/ShinyText.tsx": {"language": "TypeScript JSX", "code": 19, "comment": 0, "blank": 4}, "file:///Users/<USER>/Documents/note/python/todo/eigent-main/src/components/ui/ShinyText/ShinyText.css": {"language": "PostCSS", "code": 28, "comment": 0, "blank": 3}}