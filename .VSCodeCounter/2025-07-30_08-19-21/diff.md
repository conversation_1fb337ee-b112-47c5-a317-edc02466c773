# Diff Summary

Date : 2025-07-30 08:19:21

Directory /Users/<USER>/Documents/note/python/todo/eigent-main/src

Total : 202 files,  14312 codes, -567 comments, 559 blanks, all 14304 lines

[Summary](results.md) / [Details](details.md) / Diff Summary / [Diff Details](diff-details.md)

## Languages
| language | files | code | comment | blank | total |
| :--- | ---: | ---: | ---: | ---: | ---: |
| TypeScript JSX | 93 | 15,150 | 486 | 1,006 | 16,642 |
| TypeScript | 21 | 2,416 | 136 | 261 | 2,813 |
| PostCSS | 4 | 901 | 48 | 84 | 1,033 |
| XML | 20 | 197 | 0 | 12 | 209 |
| JSON | 4 | -194 | 0 | -2 | -196 |
| Python | 60 | -4,158 | -1,237 | -802 | -6,197 |

## Directories
| path | files | code | comment | blank | total |
| :--- | ---: | ---: | ---: | ---: | ---: |
| . | 202 | 14,312 | -567 | 559 | 14,304 |
| . (Files) | 3 | 86 | 8 | 18 | 112 |
| .. | 62 | -4,354 | -1,237 | -804 | -6,395 |
| ../backend | 62 | -4,354 | -1,237 | -804 | -6,395 |
| ../backend/app | 62 | -4,354 | -1,237 | -804 | -6,395 |
| ../backend/app (Files) | 1 | -4 | 0 | -3 | -7 |
| ../backend/app/command | 1 | -3 | 0 | -3 | -6 |
| ../backend/app/component | 10 | -332 | -65 | -67 | -464 |
| ../backend/app/component (Files) | 7 | -102 | -50 | -55 | -207 |
| ../backend/app/component/pydantic | 3 | -230 | -15 | -12 | -257 |
| ../backend/app/component/pydantic (Files) | 1 | -34 | -15 | -10 | -59 |
| ../backend/app/component/pydantic/translations | 2 | -196 | 0 | -2 | -198 |
| ../backend/app/controller | 5 | -152 | -1 | -48 | -201 |
| ../backend/app/exception | 2 | -51 | 0 | -18 | -69 |
| ../backend/app/middleware | 1 | -4 | 0 | -3 | -7 |
| ../backend/app/model | 1 | -83 | -1 | -27 | -111 |
| ../backend/app/service | 2 | -551 | -181 | -115 | -847 |
| ../backend/app/utils | 39 | -3,174 | -989 | -520 | -4,683 |
| ../backend/app/utils (Files) | 3 | -1,120 | -533 | -145 | -1,798 |
| ../backend/app/utils/listen | 1 | -152 | -7 | -19 | -178 |
| ../backend/app/utils/server | 1 | -41 | -1 | -7 | -49 |
| ../backend/app/utils/toolkit | 34 | -1,861 | -448 | -349 | -2,658 |
| api | 1 | 192 | 13 | 47 | 252 |
| assets | 22 | 199 | 0 | 12 | 211 |
| assets (Files) | 15 | 155 | 0 | 12 | 167 |
| assets/animation | 2 | 2 | 0 | 0 | 2 |
| assets/mcp | 5 | 42 | 0 | 0 | 42 |
| components | 70 | 10,385 | 333 | 733 | 11,451 |
| components (Files) | 3 | 161 | 7 | 16 | 184 |
| components/AddWorker | 3 | 1,344 | 70 | 79 | 1,493 |
| components/BottomBar | 1 | 9 | 0 | 3 | 12 |
| components/ChatBox | 10 | 1,957 | 31 | 87 | 2,075 |
| components/Folder | 1 | 336 | 8 | 12 | 356 |
| components/GlobalSearch | 1 | 65 | 0 | 6 | 71 |
| components/HistorySidebar | 2 | 599 | 12 | 29 | 640 |
| components/InstallStep | 3 | 447 | 19 | 31 | 497 |
| components/Layout | 1 | 51 | 1 | 3 | 55 |
| components/SearchAgentWrokSpace | 1 | 360 | 15 | 13 | 388 |
| components/SearchInput | 1 | 37 | 1 | 5 | 43 |
| components/TaskState | 1 | 48 | 0 | 1 | 49 |
| components/Terminal | 1 | 267 | 50 | 50 | 367 |
| components/TerminalAgentWrokSpace | 1 | 296 | 27 | 6 | 329 |
| components/Toast | 3 | 57 | 0 | 6 | 63 |
| components/TopBar | 2 | 224 | 7 | 14 | 245 |
| components/WorkFlow | 3 | 1,262 | 56 | 53 | 1,371 |
| components/WorkSpaceMenu | 1 | 350 | 10 | 13 | 373 |
| components/ui | 30 | 2,429 | 19 | 293 | 2,741 |
| components/ui (Files) | 28 | 2,382 | 19 | 286 | 2,687 |
| components/ui/ShinyText | 2 | 47 | 0 | 7 | 54 |
| components/update | 1 | 86 | 0 | 13 | 99 |
| demos | 1 | 7 | 0 | 2 | 9 |
| hooks | 2 | 26 | 0 | 9 | 35 |
| lib | 5 | 343 | 4 | 46 | 393 |
| pages | 22 | 4,681 | 143 | 249 | 5,073 |
| pages (Files) | 7 | 1,685 | 41 | 112 | 1,838 |
| pages/Setting | 15 | 2,996 | 102 | 137 | 3,235 |
| pages/Setting (Files) | 6 | 1,963 | 76 | 78 | 2,117 |
| pages/Setting/components | 9 | 1,033 | 26 | 59 | 1,118 |
| routers | 1 | 60 | 4 | 7 | 71 |
| stack | 1 | 13 | 0 | 2 | 15 |
| store | 4 | 1,601 | 115 | 136 | 1,852 |
| style | 2 | 847 | 48 | 78 | 973 |
| types | 6 | 226 | 2 | 24 | 252 |

[Summary](results.md) / [Details](details.md) / Diff Summary / [Diff Details](diff-details.md)