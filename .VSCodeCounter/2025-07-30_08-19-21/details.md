# Details

Date : 2025-07-30 08:19:21

Directory /Users/<USER>/Documents/note/python/todo/eigent-main/src

Total : 140 files,  18666 codes, 670 comments, 1363 blanks, all 20699 lines

[Summary](results.md) / Details / [Diff Summary](diff.md) / [Diff Details](diff-details.md)

## Files
| filename | language | code | comment | blank | total |
| :--- | :--- | ---: | ---: | ---: | ---: |
| [src/App.tsx](/src/App.tsx) | TypeScript JSX | 58 | 2 | 11 | 71 |
| [src/api/http.ts](/src/api/http.ts) | TypeScript | 192 | 13 | 47 | 252 |
| [src/assets/Chrome.svg](/src/assets/Chrome.svg) | XML | 6 | 0 | 1 | 7 |
| [src/assets/Edge.svg](/src/assets/Edge.svg) | XML | 20 | 0 | 1 | 21 |
| [src/assets/Folder-1.svg](/src/assets/Folder-1.svg) | XML | 33 | 0 | 1 | 34 |
| [src/assets/Folder.svg](/src/assets/Folder.svg) | XML | 33 | 0 | 1 | 34 |
| [src/assets/NoIcon.svg](/src/assets/NoIcon.svg) | XML | 6 | 0 | 1 | 7 |
| [src/assets/animation/onboarding\_success.json](/src/assets/animation/onboarding_success.json) | JSON | 1 | 0 | 0 | 1 |
| [src/assets/animation/openning\_animaiton.json](/src/assets/animation/openning_animaiton.json) | JSON | 1 | 0 | 0 | 1 |
| [src/assets/chevron\_left.svg](/src/assets/chevron_left.svg) | XML | 6 | 0 | 1 | 7 |
| [src/assets/eye-off.svg](/src/assets/eye-off.svg) | XML | 3 | 0 | 1 | 4 |
| [src/assets/eye.svg](/src/assets/eye.svg) | XML | 4 | 0 | 1 | 5 |
| [src/assets/github.svg](/src/assets/github.svg) | XML | 8 | 0 | 0 | 8 |
| [src/assets/github2.svg](/src/assets/github2.svg) | XML | 3 | 0 | 1 | 4 |
| [src/assets/google.svg](/src/assets/google.svg) | XML | 1 | 0 | 0 | 1 |
| [src/assets/logo-electron.svg](/src/assets/logo-electron.svg) | XML | 8 | 0 | 1 | 9 |
| [src/assets/logo-v1.svg](/src/assets/logo-v1.svg) | XML | 1 | 0 | 0 | 1 |
| [src/assets/logo-vite.svg](/src/assets/logo-vite.svg) | XML | 20 | 0 | 1 | 21 |
| [src/assets/mcp/Anthropic.svg](/src/assets/mcp/Anthropic.svg) | XML | 13 | 0 | 0 | 13 |
| [src/assets/mcp/Camel.svg](/src/assets/mcp/Camel.svg) | XML | 6 | 0 | 0 | 6 |
| [src/assets/mcp/Community.svg](/src/assets/mcp/Community.svg) | XML | 12 | 0 | 0 | 12 |
| [src/assets/mcp/Ellipse-25.svg](/src/assets/mcp/Ellipse-25.svg) | XML | 3 | 0 | 0 | 3 |
| [src/assets/mcp/Official.svg](/src/assets/mcp/Official.svg) | XML | 8 | 0 | 0 | 8 |
| [src/assets/rac-pause.svg](/src/assets/rac-pause.svg) | XML | 3 | 0 | 1 | 4 |
| [src/components/AddWorker/IntegrationList.tsx](/src/components/AddWorker/IntegrationList.tsx) | TypeScript JSX | 432 | 27 | 19 | 478 |
| [src/components/AddWorker/ToolSelect.tsx](/src/components/AddWorker/ToolSelect.tsx) | TypeScript JSX | 467 | 28 | 34 | 529 |
| [src/components/AddWorker/index.tsx](/src/components/AddWorker/index.tsx) | TypeScript JSX | 445 | 15 | 26 | 486 |
| [src/components/AnimationJson.tsx](/src/components/AnimationJson.tsx) | TypeScript JSX | 32 | 1 | 3 | 36 |
| [src/components/BottomBar/index.tsx](/src/components/BottomBar/index.tsx) | TypeScript JSX | 9 | 0 | 3 | 12 |
| [src/components/ChatBox/BottomInput.tsx](/src/components/ChatBox/BottomInput.tsx) | TypeScript JSX | 438 | 5 | 16 | 459 |
| [src/components/ChatBox/MarkDown.tsx](/src/components/ChatBox/MarkDown.tsx) | TypeScript JSX | 185 | 1 | 7 | 193 |
| [src/components/ChatBox/MessageCard.tsx](/src/components/ChatBox/MessageCard.tsx) | TypeScript JSX | 87 | 5 | 9 | 101 |
| [src/components/ChatBox/NoticeCard.tsx](/src/components/ChatBox/NoticeCard.tsx) | TypeScript JSX | 86 | 2 | 7 | 95 |
| [src/components/ChatBox/SummaryMarkDown.tsx](/src/components/ChatBox/SummaryMarkDown.tsx) | TypeScript JSX | 106 | 0 | 7 | 113 |
| [src/components/ChatBox/TaskCard.tsx](/src/components/ChatBox/TaskCard.tsx) | TypeScript JSX | 283 | 0 | 12 | 295 |
| [src/components/ChatBox/TaskItem.tsx](/src/components/ChatBox/TaskItem.tsx) | TypeScript JSX | 130 | 2 | 6 | 138 |
| [src/components/ChatBox/TaskType.tsx](/src/components/ChatBox/TaskType.tsx) | TypeScript JSX | 30 | 0 | 1 | 31 |
| [src/components/ChatBox/TypeCardSkeleton.tsx](/src/components/ChatBox/TypeCardSkeleton.tsx) | TypeScript JSX | 92 | 0 | 4 | 96 |
| [src/components/ChatBox/index.tsx](/src/components/ChatBox/index.tsx) | TypeScript JSX | 520 | 16 | 18 | 554 |
| [src/components/Folder/index.tsx](/src/components/Folder/index.tsx) | TypeScript JSX | 336 | 8 | 12 | 356 |
| [src/components/GlobalSearch/index.tsx](/src/components/GlobalSearch/index.tsx) | TypeScript JSX | 65 | 0 | 6 | 71 |
| [src/components/HistorySidebar/SearchInput.tsx](/src/components/HistorySidebar/SearchInput.tsx) | TypeScript JSX | 35 | 1 | 5 | 41 |
| [src/components/HistorySidebar/index.tsx](/src/components/HistorySidebar/index.tsx) | TypeScript JSX | 564 | 11 | 24 | 599 |
| [src/components/InstallStep/Carousel.tsx](/src/components/InstallStep/Carousel.tsx) | TypeScript JSX | 162 | 5 | 13 | 180 |
| [src/components/InstallStep/InstallDependencies.tsx](/src/components/InstallStep/InstallDependencies.tsx) | TypeScript JSX | 166 | 14 | 12 | 192 |
| [src/components/InstallStep/Permissions.tsx](/src/components/InstallStep/Permissions.tsx) | TypeScript JSX | 119 | 0 | 6 | 125 |
| [src/components/Layout/index.tsx](/src/components/Layout/index.tsx) | TypeScript JSX | 51 | 1 | 3 | 55 |
| [src/components/SearchAgentWrokSpace/index.tsx](/src/components/SearchAgentWrokSpace/index.tsx) | TypeScript JSX | 360 | 15 | 13 | 388 |
| [src/components/SearchHistoryDialog.tsx](/src/components/SearchHistoryDialog.tsx) | TypeScript JSX | 96 | 2 | 5 | 103 |
| [src/components/SearchInput/index.tsx](/src/components/SearchInput/index.tsx) | TypeScript JSX | 37 | 1 | 5 | 43 |
| [src/components/TaskState/index.tsx](/src/components/TaskState/index.tsx) | TypeScript JSX | 48 | 0 | 1 | 49 |
| [src/components/Terminal/index.tsx](/src/components/Terminal/index.tsx) | TypeScript JSX | 267 | 50 | 50 | 367 |
| [src/components/TerminalAgentWrokSpace/index.tsx](/src/components/TerminalAgentWrokSpace/index.tsx) | TypeScript JSX | 296 | 27 | 6 | 329 |
| [src/components/ThemeProvider.tsx](/src/components/ThemeProvider.tsx) | TypeScript JSX | 33 | 4 | 8 | 45 |
| [src/components/Toast/creditsToast.tsx](/src/components/Toast/creditsToast.tsx) | TypeScript JSX | 25 | 0 | 2 | 27 |
| [src/components/Toast/storageToast.tsx](/src/components/Toast/storageToast.tsx) | TypeScript JSX | 21 | 0 | 2 | 23 |
| [src/components/Toast/trafficToast.tsx](/src/components/Toast/trafficToast.tsx) | TypeScript JSX | 11 | 0 | 2 | 13 |
| [src/components/TopBar/index.css](/src/components/TopBar/index.css) | PostCSS | 26 | 0 | 3 | 29 |
| [src/components/TopBar/index.tsx](/src/components/TopBar/index.tsx) | TypeScript JSX | 198 | 7 | 11 | 216 |
| [src/components/WorkFlow/MarkDown.tsx](/src/components/WorkFlow/MarkDown.tsx) | TypeScript JSX | 176 | 1 | 7 | 184 |
| [src/components/WorkFlow/index.tsx](/src/components/WorkFlow/index.tsx) | TypeScript JSX | 333 | 53 | 26 | 412 |
| [src/components/WorkFlow/node.tsx](/src/components/WorkFlow/node.tsx) | TypeScript JSX | 753 | 2 | 20 | 775 |
| [src/components/WorkSpaceMenu/index.tsx](/src/components/WorkSpaceMenu/index.tsx) | TypeScript JSX | 350 | 10 | 13 | 373 |
| [src/components/ui/ShinyText/ShinyText.css](/src/components/ui/ShinyText/ShinyText.css) | PostCSS | 28 | 0 | 3 | 31 |
| [src/components/ui/ShinyText/ShinyText.tsx](/src/components/ui/ShinyText/ShinyText.tsx) | TypeScript JSX | 19 | 0 | 4 | 23 |
| [src/components/ui/accordion.tsx](/src/components/ui/accordion.tsx) | TypeScript JSX | 49 | 0 | 7 | 56 |
| [src/components/ui/alert.tsx](/src/components/ui/alert.tsx) | TypeScript JSX | 53 | 0 | 7 | 60 |
| [src/components/ui/alertDialog.tsx](/src/components/ui/alertDialog.tsx) | TypeScript JSX | 63 | 2 | 5 | 70 |
| [src/components/ui/badge.tsx](/src/components/ui/badge.tsx) | TypeScript JSX | 31 | 0 | 6 | 37 |
| [src/components/ui/button.tsx](/src/components/ui/button.tsx) | TypeScript JSX | 57 | 1 | 7 | 65 |
| [src/components/ui/card.tsx](/src/components/ui/card.tsx) | TypeScript JSX | 68 | 0 | 9 | 77 |
| [src/components/ui/carousel.tsx](/src/components/ui/carousel.tsx) | TypeScript JSX | 231 | 0 | 30 | 261 |
| [src/components/ui/command.tsx](/src/components/ui/command.tsx) | TypeScript JSX | 135 | 0 | 17 | 152 |
| [src/components/ui/dialog.tsx](/src/components/ui/dialog.tsx) | TypeScript JSX | 109 | 0 | 14 | 123 |
| [src/components/ui/dropdown-menu.tsx](/src/components/ui/dropdown-menu.tsx) | TypeScript JSX | 182 | 0 | 18 | 200 |
| [src/components/ui/input.tsx](/src/components/ui/input.tsx) | TypeScript JSX | 19 | 0 | 4 | 23 |
| [src/components/ui/label.tsx](/src/components/ui/label.tsx) | TypeScript JSX | 20 | 0 | 5 | 25 |
| [src/components/ui/popover.tsx](/src/components/ui/popover.tsx) | TypeScript JSX | 26 | 0 | 8 | 34 |
| [src/components/ui/progress-install.tsx](/src/components/ui/progress-install.tsx) | TypeScript JSX | 27 | 0 | 4 | 31 |
| [src/components/ui/progress.tsx](/src/components/ui/progress.tsx) | TypeScript JSX | 23 | 0 | 4 | 27 |
| [src/components/ui/select.tsx](/src/components/ui/select.tsx) | TypeScript JSX | 145 | 0 | 13 | 158 |
| [src/components/ui/separator.tsx](/src/components/ui/separator.tsx) | TypeScript JSX | 26 | 0 | 4 | 30 |
| [src/components/ui/sheet.tsx](/src/components/ui/sheet.tsx) | TypeScript JSX | 125 | 0 | 16 | 141 |
| [src/components/ui/sidebar.tsx](/src/components/ui/sidebar.tsx) | TypeScript JSX | 706 | 12 | 54 | 772 |
| [src/components/ui/skeleton.tsx](/src/components/ui/skeleton.tsx) | TypeScript JSX | 13 | 0 | 3 | 16 |
| [src/components/ui/sonner.tsx](/src/components/ui/sonner.tsx) | TypeScript JSX | 25 | 0 | 5 | 30 |
| [src/components/ui/switch.tsx](/src/components/ui/switch.tsx) | TypeScript JSX | 24 | 0 | 4 | 28 |
| [src/components/ui/tabs.tsx](/src/components/ui/tabs.tsx) | TypeScript JSX | 47 | 0 | 7 | 54 |
| [src/components/ui/tag.tsx](/src/components/ui/tag.tsx) | TypeScript JSX | 45 | 4 | 8 | 57 |
| [src/components/ui/textarea.tsx](/src/components/ui/textarea.tsx) | TypeScript JSX | 19 | 0 | 4 | 23 |
| [src/components/ui/toggle-group.tsx](/src/components/ui/toggle-group.tsx) | TypeScript JSX | 51 | 0 | 9 | 60 |
| [src/components/ui/toggle.tsx](/src/components/ui/toggle.tsx) | TypeScript JSX | 39 | 0 | 7 | 46 |
| [src/components/ui/tooltip.tsx](/src/components/ui/tooltip.tsx) | TypeScript JSX | 24 | 0 | 7 | 31 |
| [src/components/update/index.tsx](/src/components/update/index.tsx) | TypeScript JSX | 86 | 0 | 13 | 99 |
| [src/demos/node.ts](/src/demos/node.ts) | TypeScript | 7 | 0 | 2 | 9 |
| [src/hooks/use-app-version.tsx](/src/hooks/use-app-version.tsx) | TypeScript JSX | 11 | 0 | 4 | 15 |
| [src/hooks/use-mobile.tsx](/src/hooks/use-mobile.tsx) | TypeScript JSX | 15 | 0 | 5 | 20 |
| [src/lib/index.ts](/src/lib/index.ts) | TypeScript | 43 | 0 | 10 | 53 |
| [src/lib/llm.ts](/src/lib/llm.ts) | TypeScript | 85 | 0 | 1 | 86 |
| [src/lib/oauth.ts](/src/lib/oauth.ts) | TypeScript | 190 | 4 | 32 | 226 |
| [src/lib/share.ts](/src/lib/share.ts) | TypeScript | 20 | 0 | 1 | 21 |
| [src/lib/utils.ts](/src/lib/utils.ts) | TypeScript | 5 | 0 | 2 | 7 |
| [src/main.tsx](/src/main.tsx) | TypeScript JSX | 24 | 4 | 5 | 33 |
| [src/pages/History.tsx](/src/pages/History.tsx) | TypeScript JSX | 654 | 8 | 25 | 687 |
| [src/pages/Home.tsx](/src/pages/Home.tsx) | TypeScript JSX | 240 | 21 | 15 | 276 |
| [src/pages/Login.tsx](/src/pages/Login.tsx) | TypeScript JSX | 312 | 3 | 29 | 344 |
| [src/pages/NotFound.tsx](/src/pages/NotFound.tsx) | TypeScript JSX | 4 | 0 | 2 | 6 |
| [src/pages/Setting.tsx](/src/pages/Setting.tsx) | TypeScript JSX | 127 | 6 | 10 | 143 |
| [src/pages/Setting/API.tsx](/src/pages/Setting/API.tsx) | TypeScript JSX | 102 | 0 | 7 | 109 |
| [src/pages/Setting/General.tsx](/src/pages/Setting/General.tsx) | TypeScript JSX | 135 | 32 | 8 | 175 |
| [src/pages/Setting/MCP.tsx](/src/pages/Setting/MCP.tsx) | TypeScript JSX | 353 | 8 | 19 | 380 |
| [src/pages/Setting/MCPMarket.tsx](/src/pages/Setting/MCPMarket.tsx) | TypeScript JSX | 409 | 15 | 18 | 442 |
| [src/pages/Setting/Models.tsx](/src/pages/Setting/Models.tsx) | TypeScript JSX | 873 | 21 | 19 | 913 |
| [src/pages/Setting/Privacy.tsx](/src/pages/Setting/Privacy.tsx) | TypeScript JSX | 91 | 0 | 7 | 98 |
| [src/pages/Setting/components/IntegrationList.tsx](/src/pages/Setting/components/IntegrationList.tsx) | TypeScript JSX | 391 | 22 | 27 | 440 |
| [src/pages/Setting/components/MCPAddDialog.tsx](/src/pages/Setting/components/MCPAddDialog.tsx) | TypeScript JSX | 147 | 4 | 7 | 158 |
| [src/pages/Setting/components/MCPConfigDialog.tsx](/src/pages/Setting/components/MCPConfigDialog.tsx) | TypeScript JSX | 97 | 0 | 3 | 100 |
| [src/pages/Setting/components/MCPDeleteDialog.tsx](/src/pages/Setting/components/MCPDeleteDialog.tsx) | TypeScript JSX | 26 | 0 | 2 | 28 |
| [src/pages/Setting/components/MCPEnvDialog.tsx](/src/pages/Setting/components/MCPEnvDialog.tsx) | TypeScript JSX | 213 | 0 | 12 | 225 |
| [src/pages/Setting/components/MCPList.tsx](/src/pages/Setting/components/MCPList.tsx) | TypeScript JSX | 26 | 0 | 2 | 28 |
| [src/pages/Setting/components/MCPListItem.tsx](/src/pages/Setting/components/MCPListItem.tsx) | TypeScript JSX | 103 | 0 | 4 | 107 |
| [src/pages/Setting/components/types.ts](/src/pages/Setting/components/types.ts) | TypeScript | 20 | 0 | 1 | 21 |
| [src/pages/Setting/components/utils.ts](/src/pages/Setting/components/utils.ts) | TypeScript | 10 | 0 | 1 | 11 |
| [src/pages/SignUp.tsx](/src/pages/SignUp.tsx) | TypeScript JSX | 345 | 3 | 29 | 377 |
| [src/pages/Task.tsx](/src/pages/Task.tsx) | TypeScript JSX | 3 | 0 | 2 | 5 |
| [src/routers/index.tsx](/src/routers/index.tsx) | TypeScript JSX | 60 | 4 | 7 | 71 |
| [src/stack/client.ts](/src/stack/client.ts) | TypeScript | 13 | 0 | 2 | 15 |
| [src/store/authStore.ts](/src/store/authStore.ts) | TypeScript | 128 | 19 | 28 | 175 |
| [src/store/chatStore.ts](/src/store/chatStore.ts) | TypeScript | 1,435 | 92 | 102 | 1,629 |
| [src/store/globalStore.ts](/src/store/globalStore.ts) | TypeScript | 25 | 4 | 4 | 33 |
| [src/store/sidebarStore.ts](/src/store/sidebarStore.ts) | TypeScript | 13 | 0 | 2 | 15 |
| [src/style/index.css](/src/style/index.css) | PostCSS | 271 | 6 | 53 | 330 |
| [src/style/token.css](/src/style/token.css) | PostCSS | 576 | 42 | 25 | 643 |
| [src/types/chatbox.d.ts](/src/types/chatbox.d.ts) | TypeScript | 118 | 1 | 13 | 132 |
| [src/types/electron-updater.d.ts](/src/types/electron-updater.d.ts) | TypeScript | 9 | 0 | 2 | 11 |
| [src/types/electron.d.ts](/src/types/electron.d.ts) | TypeScript | 65 | 0 | 3 | 68 |
| [src/types/index.ts](/src/types/index.ts) | TypeScript | 29 | 0 | 2 | 31 |
| [src/types/stackframe-react.d.ts](/src/types/stackframe-react.d.ts) | TypeScript | 1 | 0 | 1 | 2 |
| [src/types/workspace.d.ts](/src/types/workspace.d.ts) | TypeScript | 4 | 1 | 3 | 8 |
| [src/vite-env.d.ts](/src/vite-env.d.ts) | TypeScript | 4 | 2 | 2 | 8 |

[Summary](results.md) / Details / [Diff Summary](diff.md) / [Diff Details](diff-details.md)